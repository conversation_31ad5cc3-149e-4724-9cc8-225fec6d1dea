"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  Users,
  UserCheck,
  UserX,
  AlertTriangle,
  UserPlus,
  BarChart3,
  ArrowRight,
  ArrowLeft,
  Sparkles,
  Target,
  Clock,
  Award,
  TrendingUp,
  Activity,
  Download,
  Eye,
  Gift,
} from "lucide-react"
import Link from "next/link"
import { useRTL } from "@/contexts/rtl-context"
import { useAppStore } from "@/stores/app-store"
import { cn } from "@/lib/utils"

interface DashboardStats {
  totalUsers: number
  presentToday: number
  absentToday: number
  consistentAttendees: number
  redFlags: number
  attendanceRate: number
  weeklyAttendance: number
  monthlyAttendance: number
}

export default function DashboardPage() {
  const { isRTL, direction } = useRTL()
  const { users, attendanceRecords } = useAppStore()
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    presentToday: 0,
    absentToday: 0,
    consistentAttendees: 0,
    redFlags: 0,
    attendanceRate: 0,
    weeklyAttendance: 0,
    monthlyAttendance: 0,
  })
  const [loading, setLoading] = useState(true)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    calculateDashboardStats()
  }, [users, attendanceRecords])

  const calculateDashboardStats = async () => {
    try {
      // Simulate API delay for better UX
      await new Promise((resolve) => setTimeout(resolve, 500))

      const today = new Date().toISOString().split("T")[0]
      const weekStart = new Date()
      weekStart.setDate(weekStart.getDate() - 7)
      const monthStart = new Date()
      monthStart.setDate(monthStart.getDate() - 30)

      // Today's attendance
      const todayRecords = attendanceRecords.filter((r) => r.date === today)
      const presentToday = todayRecords.filter((r) => r.present).length
      const absentToday = users.length - presentToday

      // Weekly attendance
      const weekRecords = attendanceRecords.filter((r) => {
        const recordDate = new Date(r.date)
        return recordDate >= weekStart && recordDate <= new Date()
      })
      const weeklyPresent = weekRecords.filter((r) => r.present).length
      const weeklyTotal = weekRecords.length
      const weeklyAttendance = weeklyTotal > 0 ? Math.round((weeklyPresent / weeklyTotal) * 100) : 0

      // Monthly attendance
      const monthRecords = attendanceRecords.filter((r) => {
        const recordDate = new Date(r.date)
        return recordDate >= monthStart && recordDate <= new Date()
      })
      const monthlyPresent = monthRecords.filter((r) => r.present).length
      const monthlyTotal = monthRecords.length
      const monthlyAttendance = monthlyTotal > 0 ? Math.round((monthlyPresent / monthlyTotal) * 100) : 0

      // Consistent attendees (80%+ attendance rate)
      const userAttendanceRates = users.map((user) => {
        const userRecords = attendanceRecords.filter((r) => r.user_id === user.id)
        const userPresent = userRecords.filter((r) => r.present).length
        const userTotal = userRecords.length
        return {
          userId: user.id,
          rate: userTotal > 0 ? (userPresent / userTotal) * 100 : 0,
        }
      })

      const consistentAttendees = userAttendanceRates.filter((u) => u.rate >= 80).length
      const redFlags = userAttendanceRates.filter((u) => u.rate < 50 && u.rate > 0).length

      const attendanceRate = users.length > 0 ? Math.round((presentToday / users.length) * 100) : 0

      setStats({
        totalUsers: users.length,
        presentToday,
        absentToday,
        consistentAttendees,
        redFlags,
        attendanceRate,
        weeklyAttendance,
        monthlyAttendance,
      })
    } catch (error) {
      console.error("Error calculating dashboard stats:", error)
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      title: "إجمالي الأعضاء",
      value: stats.totalUsers,
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      gradient: "from-blue-500 to-blue-600",
      change: "+12 هذا الشهر",
      changeType: "positive" as const,
    },
    {
      title: "الحاضرون اليوم",
      value: stats.presentToday,
      icon: UserCheck,
      color: "text-green-600",
      bgColor: "bg-green-100",
      gradient: "from-green-500 to-green-600",
      change: `${stats.attendanceRate}% معدل الحضور`,
      changeType: "positive" as const,
    },
    {
      title: "الغائبون اليوم",
      value: stats.absentToday,
      icon: UserX,
      color: "text-red-600",
      bgColor: "bg-red-100",
      gradient: "from-red-500 to-red-600",
      change: "-3 من الأسبوع الماضي",
      changeType: "positive" as const,
    },
    {
      title: "الحضور المنتظم",
      value: stats.consistentAttendees,
      icon: Award,
      color: "text-emerald-600",
      bgColor: "bg-emerald-100",
      gradient: "from-emerald-500 to-emerald-600",
      change: "80%+ معدل حضور",
      changeType: "positive" as const,
    },
    {
      title: "يحتاج متابعة",
      value: stats.redFlags,
      icon: AlertTriangle,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      gradient: "from-orange-500 to-orange-600",
      change: "أقل من 50% حضور",
      changeType: "neutral" as const,
    },
    {
      title: "المعدل الأسبوعي",
      value: `${stats.weeklyAttendance}%`,
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      gradient: "from-purple-500 to-purple-600",
      change: "آخر 7 أيام",
      changeType: "positive" as const,
    },
  ]

  const quickActions = [
    {
      title: "تسجيل الحضور",
      description: "تسجيل حضور الأعضاء للاجتماع",
      icon: UserCheck,
      href: "/attendance",
      color: "from-blue-500 to-blue-600",
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
    },
    {
      title: "إضافة عضو جديد",
      description: "إضافة عضو جديد للنظام",
      icon: UserPlus,
      href: "/add-user",
      color: "from-green-500 to-green-600",
      iconBg: "bg-green-100",
      iconColor: "text-green-600",
    },
    {
      title: "عرض التقارير",
      description: "تقارير مفصلة عن الحضور",
      icon: BarChart3,
      href: "/reports",
      color: "from-purple-500 to-purple-600",
      iconBg: "bg-purple-100",
      iconColor: "text-purple-600",
    },
    {
      title: "أعياد الميلاد",
      description: "إدارة أعياد ميلاد الأعضاء",
      icon: Gift,
      href: "/birthdays",
      color: "from-pink-500 to-pink-600",
      iconBg: "bg-pink-100",
      iconColor: "text-pink-600",
    },
  ]

  const ArrowIcon = isRTL ? ArrowLeft : ArrowRight

  if (!mounted) return null

  return (
    <div className="p-6 space-y-8 page-transition font-cairo" dir={direction}>
      {/* Header */}
      <div className="animate-fade-in">
        <div className={cn("flex items-center justify-between", isRTL ? "flex-row-reverse" : "flex-row")}>
          <div>
            <div className={cn("flex items-center gap-3 mb-2", isRTL ? "flex-row-reverse" : "flex-row")}>
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                <Target className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-heading gradient-text">لوحة التحكم</h1>
              <Sparkles className="h-6 w-6 text-yellow-500 animate-pulse" />
            </div>
            <p className={cn("text-gray-600 text-lg text-body", isRTL ? "text-right" : "text-left")}>
              نظرة عامة شاملة على نظام حضور الشباب
            </p>
            <div className={cn("flex items-center gap-2 mt-2", isRTL ? "flex-row-reverse" : "flex-row")}>
              <Clock className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-500">آخر تحديث: {new Date().toLocaleString("ar-EG")}</span>
            </div>
          </div>

          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              تصدير التقرير
            </Button>
            <Button variant="outline">
              <Eye className="h-4 w-4 mr-2" />
              عرض مفصل
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card, index) => (
          <Card
            key={index}
            className="hover-lift border-0 shadow-soft bg-white/80 backdrop-blur-sm animate-scale-in overflow-hidden relative group"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {/* Background Gradient */}
            <div
              className={`absolute inset-0 bg-gradient-to-br ${card.gradient} opacity-5 group-hover:opacity-10 transition-opacity`}
            />

            <CardHeader
              className={cn(
                "flex items-center justify-between space-y-0 pb-3 relative",
                isRTL ? "flex-row-reverse" : "flex-row",
              )}
            >
              <CardTitle
                className={cn("text-sm font-medium text-gray-600 font-cairo", isRTL ? "text-right" : "text-left")}
              >
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor} hover-scale group-hover:scale-110 transition-transform`}>
                <card.icon className={`h-5 w-5 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent className="relative">
              <div className={cn("flex items-baseline gap-2", isRTL ? "flex-row-reverse" : "flex-row")}>
                <div className="text-3xl font-bold text-gray-900">
                  {loading ? <div className="w-12 h-8 bg-gray-200 rounded loading-shimmer"></div> : card.value}
                </div>
                {!loading && (
                  <div
                    className={cn(
                      "text-xs px-2 py-1 rounded-full",
                      card.changeType === "positive"
                        ? "bg-green-100 text-green-700"
                        : card.changeType === "negative"
                          ? "bg-red-100 text-red-700"
                          : "bg-gray-100 text-gray-700",
                    )}
                  >
                    {card.change}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
        {quickActions.map((action, index) => (
          <Link key={index} href={action.href}>
            <Card
              className="hover-lift border-0 shadow-soft bg-white/80 backdrop-blur-sm animate-slide-in-right cursor-pointer group overflow-hidden relative h-full"
              style={{ animationDelay: `${index * 0.1 + 0.5}s` }}
            >
              {/* Background Gradient on Hover */}
              <div
                className={`absolute inset-0 bg-gradient-to-br ${action.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}
              />

              <CardHeader className="relative">
                <div className={cn("flex items-center justify-between", isRTL ? "flex-row-reverse" : "flex-row")}>
                  <div
                    className={`p-3 rounded-xl ${action.iconBg} group-hover:scale-110 transition-transform duration-300`}
                  >
                    <action.icon className={`h-6 w-6 ${action.iconColor}`} />
                  </div>
                  <ArrowIcon className="h-5 w-5 text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all duration-300" />
                </div>
                <CardTitle
                  className={cn(
                    "text-lg font-bold text-gray-900 group-hover:text-gray-700 transition-colors",
                    isRTL ? "text-right" : "text-left",
                  )}
                >
                  {action.title}
                </CardTitle>
                <CardDescription className={cn("text-gray-600 text-body", isRTL ? "text-right" : "text-left")}>
                  {action.description}
                </CardDescription>
              </CardHeader>
            </Card>
          </Link>
        ))}
      </div>

      {/* Additional Info Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card
          className="hover-lift border-0 shadow-soft bg-white/80 backdrop-blur-sm animate-fade-in"
          style={{ animationDelay: "0.8s" }}
        >
          <CardHeader>
            <div className={cn("flex items-center gap-3", isRTL ? "flex-row-reverse" : "flex-row")}>
              <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg">
                <Activity className="h-5 w-5 text-white" />
              </div>
              <CardTitle className={cn("font-medium font-cairo", isRTL ? "text-right" : "text-left")}>
                إحصائيات الأداء
              </CardTitle>
            </div>
            <CardDescription className={cn("text-gray-600 text-body", isRTL ? "text-right" : "text-left")}>
              معلومات مهمة عن أداء النظام هذا الأسبوع
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg hover-scale">
                <span
                  className={cn("text-sm font-medium text-gray-700 font-cairo", isRTL ? "text-right" : "text-left")}
                >
                  معدل الحضور الأسبوعي
                </span>
                <span className="font-bold text-blue-600">{stats.weeklyAttendance}%</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg hover-scale">
                <span
                  className={cn("text-sm font-medium text-gray-700 font-cairo", isRTL ? "text-right" : "text-left")}
                >
                  الحضور المنتظم
                </span>
                <span className="font-bold text-green-600">{stats.consistentAttendees} عضو</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg hover-scale">
                <span
                  className={cn("text-sm font-medium text-gray-700 font-cairo", isRTL ? "text-right" : "text-left")}
                >
                  يحتاج متابعة
                </span>
                <span className="font-bold text-orange-600">{stats.redFlags} عضو</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg hover-scale">
                <span
                  className={cn("text-sm font-medium text-gray-700 font-cairo", isRTL ? "text-right" : "text-left")}
                >
                  المعدل الشهري
                </span>
                <span className="font-bold text-purple-600">{stats.monthlyAttendance}%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className="hover-lift border-0 shadow-soft bg-white/80 backdrop-blur-sm animate-fade-in"
          style={{ animationDelay: "0.9s" }}
        >
          <CardHeader>
            <div className={cn("flex items-center gap-3", isRTL ? "flex-row-reverse" : "flex-row")}>
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                <Sparkles className="h-5 w-5 text-white" />
              </div>
              <CardTitle className={cn("font-medium font-cairo", isRTL ? "text-right" : "text-left")}>
                الميزات الجديدة
              </CardTitle>
            </div>
            <CardDescription className={cn("text-gray-600 text-body", isRTL ? "text-right" : "text-left")}>
              آخر التحديثات والميزات المضافة للنظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg hover-scale">
                <div className="w-2 h-2 bg-purple-500 rounded-full flex-shrink-0"></div>
                <span
                  className={cn("text-sm font-medium text-gray-700 font-cairo", isRTL ? "text-right" : "text-left")}
                >
                  تحسينات في واجهة المستخدم
                </span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg hover-scale">
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                <span
                  className={cn("text-sm font-medium text-gray-700 font-cairo", isRTL ? "text-right" : "text-left")}
                >
                  إضافة نظام أعياد الميلاد
                </span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg hover-scale">
                <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                <span
                  className={cn("text-sm font-medium text-gray-700 font-cairo", isRTL ? "text-right" : "text-left")}
                >
                  تقارير محسنة مع الرسوم البيانية
                </span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg hover-scale">
                <div className="w-2 h-2 bg-yellow-500 rounded-full flex-shrink-0"></div>
                <span
                  className={cn("text-sm font-medium text-gray-700 font-cairo", isRTL ? "text-right" : "text-left")}
                >
                  تصدير البيانات بصيغ متعددة
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
