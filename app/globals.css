@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Font Variables */
  --font-cairo: var(--font-cairo);

  /* Modern Color Palette */
  --primary: 220 91% 56%;
  --primary-foreground: 0 0% 98%;
  --secondary: 220 14% 96%;
  --secondary-foreground: 220 9% 46%;
  --accent: 220 14% 96%;
  --accent-foreground: 220 9% 46%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 98%;
  --muted: 220 14% 96%;
  --muted-foreground: 220 9% 46%;
  --card: 0 0% 100%;
  --card-foreground: 220 13% 13%;
  --popover: 0 0% 100%;
  --popover-foreground: 220 13% 13%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --ring: 220 91% 56%;
  --background: 220 17% 97%;
  --foreground: 220 13% 13%;
  --radius: 0.75rem;

  /* Custom Design Tokens */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Animation Variables */
  --animation-fast: 0.15s;
  --animation-normal: 0.3s;
  --animation-slow: 0.5s;
  --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
  --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);

  /* RTL Variables */
  --sidebar-width: 18rem;
  --sidebar-width-collapsed: 4rem;
}

.dark {
  --background: 220 13% 9%;
  --foreground: 220 17% 97%;
  --card: 220 13% 13%;
  --card-foreground: 220 17% 97%;
  --popover: 220 13% 13%;
  --popover-foreground: 220 17% 97%;
  --primary: 220 91% 56%;
  --primary-foreground: 220 13% 9%;
  --secondary: 220 13% 18%;
  --secondary-foreground: 220 17% 97%;
  --muted: 220 13% 18%;
  --muted-foreground: 220 9% 64%;
  --accent: 220 13% 18%;
  --accent-foreground: 220 17% 97%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 220 17% 97%;
  --border: 220 13% 18%;
  --input: 220 13% 18%;
  --ring: 220 91% 56%;
}

* {
  @apply border-border;
}

body {
  @apply bg-background text-foreground;
  font-family: var(--font-cairo), system-ui, -apple-system, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* RTL Base Styles */
[dir="rtl"] {
  text-align: right;
}

[dir="ltr"] {
  text-align: left;
}

/* RTL Typography */
.font-cairo {
  font-family: var(--font-cairo), system-ui, -apple-system, sans-serif;
}

.text-heading {
  font-family: var(--font-cairo), system-ui, -apple-system, sans-serif;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.text-body {
  font-family: var(--font-cairo), system-ui, -apple-system, sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

/* RTL Layout Utilities */
.rtl-container {
  direction: rtl;
  text-align: right;
}

.ltr-container {
  direction: ltr;
  text-align: left;
}

/* RTL Flexbox Utilities */
.flex-rtl {
  display: flex;
}

[dir="rtl"] .flex-rtl {
  flex-direction: row-reverse;
}

.flex-rtl-col {
  display: flex;
  flex-direction: column;
}

/* RTL Spacing Utilities */
.mr-rtl-2 {
  margin-right: 0.5rem;
}

[dir="rtl"] .mr-rtl-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

.ml-rtl-2 {
  margin-left: 0.5rem;
}

[dir="rtl"] .ml-rtl-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

.mr-rtl-3 {
  margin-right: 0.75rem;
}

[dir="rtl"] .mr-rtl-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

.ml-rtl-3 {
  margin-left: 0.75rem;
}

[dir="rtl"] .ml-rtl-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

.pr-rtl-4 {
  padding-right: 1rem;
}

[dir="rtl"] .pr-rtl-4 {
  padding-right: 0;
  padding-left: 1rem;
}

.pl-rtl-4 {
  padding-left: 1rem;
}

[dir="rtl"] .pl-rtl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

/* RTL Border Utilities */
.border-r-rtl {
  border-right: 1px solid hsl(var(--border));
}

[dir="rtl"] .border-r-rtl {
  border-right: none;
  border-left: 1px solid hsl(var(--border));
}

.border-l-rtl {
  border-left: 1px solid hsl(var(--border));
}

[dir="rtl"] .border-l-rtl {
  border-left: none;
  border-right: 1px solid hsl(var(--border));
}

/* RTL Position Utilities */
.right-rtl-0 {
  right: 0;
}

[dir="rtl"] .right-rtl-0 {
  right: auto;
  left: 0;
}

.left-rtl-0 {
  left: 0;
}

[dir="rtl"] .left-rtl-0 {
  left: auto;
  right: 0;
}

/* RTL Transform Utilities */
.translate-x-rtl-full {
  transform: translateX(100%);
}

[dir="rtl"] .translate-x-rtl-full {
  transform: translateX(-100%);
}

.translate-x-rtl-0 {
  transform: translateX(0);
}

/* RTL Animation Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRightRTL {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeftRTL {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* RTL Animation Classes */
.animate-fade-in {
  animation: fadeIn var(--animation-normal) var(--ease-out-cubic);
}

.animate-slide-in-right {
  animation: slideInRight var(--animation-normal) var(--ease-out-cubic);
}

[dir="rtl"] .animate-slide-in-right {
  animation: slideInRightRTL var(--animation-normal) var(--ease-out-cubic);
}

.animate-slide-in-left {
  animation: slideInLeft var(--animation-normal) var(--ease-out-cubic);
}

[dir="rtl"] .animate-slide-in-left {
  animation: slideInLeftRTL var(--animation-normal) var(--ease-out-cubic);
}

.animate-scale-in {
  animation: scaleIn var(--animation-normal) var(--ease-out-cubic);
}

/* RTL Hover Effects */
.hover-lift {
  transition: transform var(--animation-fast) var(--ease-out-cubic), box-shadow var(--animation-fast)
    var(--ease-out-cubic);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.hover-scale {
  transition: transform var(--animation-fast) var(--ease-out-cubic);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* RTL Sidebar Specific Styles */
.sidebar-rtl {
  position: fixed;
  top: 0;
  bottom: 0;
  width: var(--sidebar-width);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-left: 1px solid hsl(var(--border));
  right: 0;
  transform: translateX(0);
  transition: transform 0.3s var(--ease-out-cubic);
  z-index: 40;
}

[dir="rtl"] .sidebar-rtl {
  right: 0;
  left: auto;
  border-left: none;
  border-right: 1px solid hsl(var(--border));
}

.sidebar-rtl.closed {
  transform: translateX(100%);
}

[dir="rtl"] .sidebar-rtl.closed {
  transform: translateX(-100%);
}

/* RTL Main Content */
.main-content-rtl {
  margin-right: var(--sidebar-width);
  transition: margin 0.3s var(--ease-out-cubic);
}

[dir="rtl"] .main-content-rtl {
  margin-right: 0;
  margin-left: var(--sidebar-width);
}

.main-content-rtl.sidebar-closed {
  margin-right: 0;
}

[dir="rtl"] .main-content-rtl.sidebar-closed {
  margin-left: 0;
}

/* RTL Mobile Styles */
@media (max-width: 768px) {
  .sidebar-rtl {
    transform: translateX(100%);
  }

  [dir="rtl"] .sidebar-rtl {
    transform: translateX(-100%);
  }

  .sidebar-rtl.open {
    transform: translateX(0);
  }

  .main-content-rtl {
    margin-right: 0;
  }

  [dir="rtl"] .main-content-rtl {
    margin-left: 0;
  }
}

/* RTL Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* RTL Loading States */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    hsl(var(--muted)) 0%,
    hsl(var(--muted-foreground) / 0.1) 50%,
    hsl(var(--muted)) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* RTL Focus States */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
}

/* RTL Button Variants */
.btn-gradient {
  background: var(--gradient-primary);
  transition: all var(--animation-fast) var(--ease-out-cubic);
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

/* RTL Responsive Utilities */
@media (max-width: 768px) {
  .hover-lift:hover {
    transform: none;
    box-shadow: none;
  }

  .hover-scale:hover {
    transform: none;
  }
}

/* RTL Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  * {
    animation: none !important;
    transition: none !important;
  }
}

/* RTL Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
