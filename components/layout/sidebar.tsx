"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  LayoutDashboard,
  Users,
  UserPlus,
  ClipboardCheck,
  BarChart3,
  Calendar,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  Sparkles,
  Globe,
} from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { useRTL } from "@/contexts/rtl-context"

const navigation = [
  {
    name: "لوحة التحكم",
    href: "/dashboard",
    icon: LayoutDashboard,
    color: "text-blue-600",
    bgColor: "bg-blue-100",
    description: "نظرة عامة على النظام",
  },
  {
    name: "تسجيل الحضور",
    href: "/attendance",
    icon: Clipboard<PERSON>heck,
    color: "text-green-600",
    bgColor: "bg-green-100",
    description: "تسجيل حضور الأعضاء",
  },
  {
    name: "إضافة عضو جديد",
    href: "/add-user",
    icon: UserPlus,
    color: "text-purple-600",
    bgColor: "bg-purple-100",
    description: "إضافة عضو للنظام",
  },
  {
    name: "السنة الأولى",
    href: "/users/1",
    icon: Users,
    color: "text-indigo-600",
    bgColor: "bg-indigo-100",
    description: "طلاب السنة الأولى",
  },
  {
    name: "السنة الثانية",
    href: "/users/2",
    icon: Users,
    color: "text-cyan-600",
    bgColor: "bg-cyan-100",
    description: "طلاب السنة الثانية",
  },
  {
    name: "السنة الثالثة",
    href: "/users/3",
    icon: Users,
    color: "text-teal-600",
    bgColor: "bg-teal-100",
    description: "طلاب السنة الثالثة",
  },
  {
    name: "السنة الرابعة",
    href: "/users/4",
    icon: Users,
    color: "text-emerald-600",
    bgColor: "bg-emerald-100",
    description: "طلاب السنة الرابعة",
  },
  {
    name: "التقارير والإحصائيات",
    href: "/reports",
    icon: BarChart3,
    color: "text-orange-600",
    bgColor: "bg-orange-100",
    description: "تقارير مفصلة",
  },
  {
    name: "أعياد الميلاد",
    href: "/birthdays",
    icon: Calendar,
    color: "text-pink-600",
    bgColor: "bg-pink-100",
    description: "إدارة أعياد الميلاد",
  },
  {
    name: "الإعدادات",
    href: "/settings",
    icon: Settings,
    color: "text-gray-600",
    bgColor: "bg-gray-100",
    description: "إعدادات النظام",
  },
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()
  const { signOut, user } = useAuth()
  const { isRTL, direction, toggleDirection } = useRTL()
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    // Close mobile menu when route changes
    setIsMobileOpen(false)
  }, [pathname])

  if (!mounted) return null

  const ChevronIcon = isRTL ? ChevronLeft : ChevronRight

  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="icon"
        className={cn(
          "md:hidden fixed top-4 z-50 bg-white/80 backdrop-blur-sm shadow-medium hover-lift",
          isRTL ? "right-4" : "left-4",
        )}
        onClick={() => setIsMobileOpen(!isMobileOpen)}
      >
        {isMobileOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 z-40 w-72 bg-white/95 backdrop-blur-sm transform transition-all duration-500 ease-in-out-cubic shadow-large",
          isRTL ? "right-0 border-l border-gray-200/50" : "left-0 border-r border-gray-200/50",
          // Mobile states
          isMobileOpen
            ? "translate-x-0"
            : isRTL
              ? "translate-x-full md:translate-x-0"
              : "-translate-x-full md:translate-x-0",
          className,
        )}
        dir={direction}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between h-20 px-6 border-b border-gray-200/50 bg-gradient-to-r from-blue-50 to-purple-50">
            <div className="flex items-center gap-3 animate-scale-in">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl blur-sm opacity-30"></div>
                <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-xl">
                  <Users className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className={cn("flex flex-col", isRTL ? "items-end" : "items-start")}>
                <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  نظام الحضور
                </span>
                <div className="flex items-center gap-1 mt-1">
                  <Sparkles className="h-3 w-3 text-yellow-500" />
                  <span className="text-xs text-gray-500">الإصدار 2.0</span>
                </div>
              </div>
            </div>

            {/* Direction Toggle */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleDirection}
              className="text-gray-500 hover:text-gray-700 hover-scale"
              title={isRTL ? "Switch to LTR" : "Switch to RTL"}
            >
              <Globe className="h-4 w-4" />
            </Button>
          </div>

          {/* Navigation */}
          <ScrollArea className="flex-1 px-4 py-6">
            <nav className="space-y-2">
              {navigation.map((item, index) => {
                const isActive = pathname === item.href
                return (
                  <div
                    key={item.name}
                    className="animate-slide-in-right"
                    style={{ animationDelay: `${index * 0.05}s` }}
                  >
                    <Link
                      href={item.href}
                      className={cn(
                        "group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 relative overflow-hidden",
                        isActive
                          ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-medium"
                          : "text-gray-700 hover:bg-gray-50 hover-lift",
                        isRTL ? "text-right" : "text-left",
                      )}
                      onClick={() => setIsMobileOpen(false)}
                      onMouseEnter={() => setHoveredItem(item.name)}
                      onMouseLeave={() => setHoveredItem(null)}
                    >
                      {/* Background Animation */}
                      {!isActive && (
                        <div
                          className={cn(
                            "absolute inset-0 bg-gradient-to-r from-blue-50 to-purple-50 opacity-0 transition-opacity duration-300",
                            hoveredItem === item.name && "opacity-100",
                          )}
                        />
                      )}

                      {/* Icon */}
                      <div
                        className={cn(
                          "relative p-2 rounded-lg transition-all duration-300",
                          isActive ? "bg-white/20" : item.bgColor,
                          isRTL ? "ml-3" : "mr-3",
                        )}
                      >
                        <item.icon
                          className={cn("h-5 w-5 transition-all duration-300", isActive ? "text-white" : item.color)}
                        />
                      </div>

                      {/* Text */}
                      <div className="flex-1 relative min-w-0">
                        <span className="block font-medium truncate">{item.name}</span>
                        <span
                          className={cn(
                            "text-xs opacity-70 transition-opacity duration-300 block truncate",
                            isActive ? "text-white/80" : "text-gray-500",
                          )}
                        >
                          {item.description}
                        </span>
                      </div>

                      {/* Arrow */}
                      <ChevronIcon
                        className={cn(
                          "h-4 w-4 transition-all duration-300 flex-shrink-0",
                          isActive ? "text-white transform rotate-180" : "text-gray-400 group-hover:text-gray-600",
                          isRTL ? "mr-2" : "ml-2",
                        )}
                      />
                    </Link>
                  </div>
                )
              })}
            </nav>
          </ScrollArea>

          {/* User info and logout */}
          <div className="p-4 border-t border-gray-200/50 bg-gradient-to-r from-gray-50 to-blue-50">
            <div className="flex items-center justify-between p-3 rounded-xl bg-white/50 backdrop-blur-sm hover-lift">
              <div className={cn("flex items-center gap-3", isRTL ? "flex-row-reverse" : "flex-row")}>
                <div className="relative">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{user?.email?.charAt(0).toUpperCase()}</span>
                  </div>
                  <div
                    className={cn(
                      "absolute w-4 h-4 bg-green-500 rounded-full border-2 border-white",
                      isRTL ? "-bottom-1 -left-1" : "-bottom-1 -right-1",
                    )}
                  ></div>
                </div>
                <div className={cn("flex-1 min-w-0", isRTL ? "text-right" : "text-left")}>
                  <p className="text-sm font-medium text-gray-900 truncate">مدير النظام</p>
                  <p className="text-xs text-gray-500 truncate">{user?.email}</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={signOut}
                className="text-gray-500 hover:text-red-600 hover:bg-red-50 transition-all duration-300 hover-scale flex-shrink-0"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/20 backdrop-blur-sm md:hidden transition-opacity duration-300"
          onClick={() => setIsMobileOpen(false)}
        />
      )}
    </>
  )
}
